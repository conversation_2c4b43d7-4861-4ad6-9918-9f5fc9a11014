import tensorflow as tf
tf.compat.v1.disable_eager_execution()
import numpy as np
import utils
from Module import Net

# 加载数据
print("Loading data...")
D_load = utils.Load_Data([63, 27, 10])
D_load.load_npz('Twins38.combined.npz')
D_load.split_data()

print("Data shapes:")
for k, v in D_load.train.items():
    print(f"{k}: {v.shape}, dtype: {v.dtype}")

# 检查数据范围
print("\nData ranges:")
for k, v in D_load.train.items():
    print(f"{k}: min={np.min(v):.6f}, max={np.max(v):.6f}, has_nan={np.any(np.isnan(v))}")

# 创建网络
print("\nCreating network...")
train = D_load.train
net = Net(train['x'].shape[0], train['x'].shape[1], type('FLAGS', (), {
    'seed': 888,
    'activation': 'elu',
    'var_from': 'get_variable',
    'rep_dim': 64,
    'rep_layer': 7,
    't_dim': 64,
    't_layer': 3,
    'y_dim': 64,
    'y_layer': 7,
    'select_layer': -1,
    'batch_norm': 1,
    'rep_normalization': 1,
    'reweight_sample': 1,
    'use_p_correction': 1,
    't_is_binary': 1,
    'y_is_binary': 1,
    'p_alpha': 1e-2,
    'p_beta': 1e-3,
    'p_gamma': 1e-3,
    'p_mu': 5,
    'p_lambda': 5,
    'lr': 0.0001,
    'decay_rate': 1.0
})())

# 创建会话
print("Creating session...")
config = tf.compat.v1.ConfigProto()
config.gpu_options.allow_growth = True
sess = tf.compat.v1.Session(config=config)
tf.compat.v1.global_variables_initializer().run(session=sess)

# 测试前向传播
print("\nTesting forward pass...")
t_threshold = 0.5
ys = np.concatenate((train['y'], D_load.valid['y']), axis=0)
y_threshold = np.median(ys)

feed_dict = {
    net.x: train['x'][:100],  # 只用前100个样本测试
    net.t: train['t'][:100],
    net.y: train['y'][:100],
    net.do_in: 1.0,
    net.do_out: 1.0,
    net.p_t: 0.5,
    net.I: train['I'][:100],
    net.t_threshold: t_threshold,
    net.y_threshold: y_threshold
}

try:
    # 逐步检查各个损失组件
    print("Checking loss components...")
    
    loss_R = sess.run(net.loss_R, feed_dict=feed_dict)
    print(f"loss_R: {loss_R}")
    
    loss_A = sess.run(net.loss_A, feed_dict=feed_dict)
    print(f"loss_A: {loss_A}")
    
    loss_I = sess.run(net.loss_I, feed_dict=feed_dict)
    print(f"loss_I: {loss_I}")
    
    loss_O = sess.run(net.loss_O, feed_dict=feed_dict)
    print(f"loss_O: {loss_O}")
    
    loss_Reg = sess.run(net.loss_Reg, feed_dict=feed_dict)
    print(f"loss_Reg: {loss_Reg}")
    
    total_loss = sess.run(net.loss, feed_dict=feed_dict)
    print(f"total_loss: {total_loss}")
    
except Exception as e:
    print(f"Error during forward pass: {e}")
    import traceback
    traceback.print_exc()

sess.close()
