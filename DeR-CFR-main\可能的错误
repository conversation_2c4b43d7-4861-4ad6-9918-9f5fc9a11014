下面这轮 `NaN` 的真正元凶有三大类——**重复定义、数值保护缺口、以及数据本身**。它们交织在一起，使得你加的任何单点修补都被“另一份旧代码”或“另一条分支”悄悄抵消。逐条排查即可彻底消灭 `NaN`。

---

## 1 一文件多定义：谁在“偷偷”覆盖你的修补？

### ① `utils.py`　出现 **3 版** `safe_mean`

* 第一版已经用 `tf.math.divide_no_nan`（安全）
* 第二、三版又回到了旧的 `tf.cond` + `reduce_mean` 方案

> Python 解释器只认**最后出现**的同名函数，所以如果旧版本在文件尾部，它会把你刚写好的安全版本完全覆盖——前向一遇到空 bucket 又回到 `reduce_mean([])` → `NaN`。

### ② `Module.py`　同时保留 **3-4 个** `class Net`

* 早期版本（带 sample\_weight 截断）
* 中期版本（**没有**再截断 sample\_weight）
* 末尾版本还加了 `check_numerics` 但前面逻辑没同步改

> import 时最后那个 `Net` 生效；如果它恰好是“旧逻辑”，你所有修补都白做。

**✅ 一键式解决**

* **删掉** `utils.py` 里除“divide\_no\_nan 版本”之外的所有 `safe_mean`。
* **保留** `Module.py` 里**唯一一份** `class Net`，并确认这一份集成了你想要的所有改动（sample\_weight 截断、loss check 等）。

---

## 2 数值仍可溢出的三个细节

| 触发点                                                                                              | 可能溢出/变 NaN 的原因                                                                                                                                                     | 修复建议 |
| ------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---- |
| **IPM 项的平方求和**  `tf.square(2·p·mean)`<br>当 `mean≈10 – 30` 时马上上升到 `1e5+`；64 维求和即达 `1e7`，再叠几次梯度就溢出 | 对 `mean_*` 先做 `tf.clip_by_value(mean, -20, 20)`；或者把 `p_alpha/p_beta` 至少各再乘 `1e-2`                                                                                  |      |
| **BatchNorm**  `tf.nn.batch_normalization(out, …, ε=1e-5)`<br>单样本或极窄方差时 `(out-μ)/√σ²` 可能达 1e4 级  | 使用 `tf.layers.batch_normalization`（带 moving var）**或**把 `variance_epsilon` 提到 `1e-3`                                                                                |      |
| **样本权重**　虽然有 `relu+clip(0,10)`，但优化后仍可跳出范围（因为 clip 在创建时只执行一次）                                     | 在 `train_balance` 后面加<br>`with tf.control_dependencies([self.train_balance]): self.weight_clip = tf.assign(sample_weight, tf.clip_by_value(sample_weight,0.,10.))` |      |

---

## 3 数据本身别带 `NaN / Inf`

`y_threshold = np.median(ys)` 如果 `ys` 里本来就含 `NaN`，阈值也会是 `NaN`，随后所有比较 (`y >= NaN`) 都是 *False*，四个 bucket 立刻变空——再一次回到 `reduce_mean([])` → `NaN`。

**检查方法**（启动脚本最前面先跑）：

```python
for k,v in train.items():
    assert np.isfinite(v).all(), f"{k} contains NaN/Inf"
```

---

## 4 一条最保险的调试路线

1. **单步执行**
   在 `Train.py` 的 `sess.run(net.train …)` 前插

   ```python
   sess.run(net.loss_check, feed_dict=batch_dict)
   ```

   若仍报 `NaN`，TensorFlow 会精确指出是哪一个 op。

2. **梯度裁剪**

   ```python
   opt      = tf.compat.v1.train.AdamOptimizer(lr)
   grads,vs = zip(*opt.compute_gradients(net.loss))
   grads    = [tf.clip_by_value(g,-1.,1.) for g in grads]
   train_op = opt.apply_gradients(zip(grads,vs))
   ```

3. **确认 batch 不为空**

   ```python
   assert (t>=0.5).sum()>0 and (t<0.5).sum()>0
   ```

---

### 总结

> **核心就是：先把 *重复定义* 清干净，再给 *极值* 上限、给 *样本* 做 sanity-check。**
> 这三步做完，`loss` 就会从 `NaN` 变成一个正常且可下降的数字。如果还有问题，把出错的 op 名字贴出来，我再帮你精准定位。
