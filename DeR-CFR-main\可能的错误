**为什么会一直 `loss = nan`？**

1. **在 IPM 和 Loss 中对空张量取 `reduce_mean` 或做除法**
   每个 mini-batch 先按治疗阈值 (`t_threshold=0.5`) 把样本分成多组 (`i0/i1`, `i_1_1` …)，然后对每组求均值：

   ```python
   mean_C_0 = tf.reduce_mean(self.sample_weight_0 * self.rep_C_0, axis=0)
   mean_C_1 = tf.reduce_mean(self.sample_weight_1 * self.rep_C_1, axis=0)
   ```

   如果当前 batch **恰好没有** `t<0.5` 或 `t≥0.5` 的样本，`self.rep_C_0`（或 `rep_C_1`）就是空张量，`reduce_mean` 会直接返回 `NaN`，随后 `IPM_C / IPM_I / IPM_A` 乃至总 `loss` 都变成 `NaN`。
   同样的问题也出现在工具变量四分组的均值计算。

2. **`loss_w` 对 0 做除法**
   当启用 `reweight_sample` 时，代码还会计算

   ```python
   self.loss_w = tf.square(tf.reduce_sum(self.sample_weight_0) /
                           tf.reduce_sum(1.0-self.t) - 1.0) + ...
   ```

   若一个 batch 中某个治疗组计数为 0，分母为 0 → `Inf` → `NaN`。

3. **潜在的 `tf.concat(self.rep_I, axis=1)` 错误**
   在构建仅依赖工具变量的治疗网络时用的是

   ```python
   tf.concat(self.rep_I, axis=1)
   ```

   `self.rep_I` 只是一个张量而不是张量列表，严格来说这行会触发 shape/类型错误（TensorFlow 2.x/1.x 在图构建阶段就会抛出）。即便当前环境默默接受，它也不是你想要的行为；应直接用 `self.rep_I` 或 `tf.expand_dims(self.rep_I,-1)`。

---

### 造成 “loss = nan, y\_acc ≈ 0.858 不变” 的链条

空组 → `mean_*` 变 `NaN` → `IPM_*` 变 `NaN` → **梯度里含 `NaN`** → Adam 更新后所有可训练权重变 `NaN` → 网络输出恒定，准确率只能停留在基线水平（数据中正-负样本比例约 0.858）。

---

## 修复建议

| 问题                              | 快速修补                                                                                                     | 更彻底的解决方案                                                                                                 |
| ------------------------------- | -------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- |
| 空张量 `reduce_mean`               | 用安全平均：<br>`count = tf.maximum(tf.shape(x)[0], 1)`<br>`mean = tf.reduce_sum(x,0)/tf.cast(count, x.dtype)` | **采样改进**：写一个 *stratified* batch generator，保证每个 batch 至少含 1 个 `t=0` 和 1 个 `t=1`（以及四分组时的 1-1/1-0/0-1/0-0）。 |
| 0 做除法                           | `denom = tf.reduce_sum(self.t) + 1e-8` 等，在分母加 ε                                                          | 同上，分层采样可根本避免出现 0。                                                                                        |
| `tf.concat(self.rep_I, axis=1)` | 改成 `self.rep_I`                                                                                          | 确认同样的错误在 `self.rep_A` 处也要改。                                                                              |
| 梯度爆炸/`NaN` 扩散                   | 在 `train_op` 前后插入 `tf.check_numerics` 或开启 `tf.debugging.enable_check_numerics()`，训练时第一时间捕获并定位。           | 若仍偶发 `NaN`，可把学习率调小或对 `IPM_*` 做 clip。                                                                     |

**最简单可行的修改**（示例）：

```python
def safe_mean(x):
    n = tf.shape(x)[0]
    return tf.cond(n > 0,
                   lambda: tf.reduce_mean(x, axis=0),
                   lambda: tf.zeros_like(x[0]))

mean_C_0 = safe_mean(self.sample_weight_0 * self.rep_C_0)
mean_C_1 = safe_mean(self.sample_weight_1 * self.rep_C_1)
...
den_t  = tf.reduce_sum(self.t)          + 1e-8
den_nt = tf.reduce_sum(1.0 - self.t)    + 1e-8
self.loss_w = tf.square(tf.reduce_sum(self.sample_weight_0)/den_nt - 1.0) + ...
```

配合 **分层 batch 采样**（或把 `batch_size` 设得足够小以减少空组概率），即可根除 `NaN` 问题并让 `loss` 真正下降。
