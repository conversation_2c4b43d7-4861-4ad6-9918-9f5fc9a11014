导致 **loss 一直变成 `nan`** 的根源不是优化器或学习率，而是 **计算中仍然有 *空张量做 `tf.reduce_mean`***——一旦某个 bucket 没有样本，`reduce_mean()` 会得到 `nan`，随后在前向图中一路传播，最终让整个 `loss` 都变成 `nan`。
下面 3 处仍然保留着这种隐患（列出的是在最新版 *Module.py* 里真正被执行的那一段，而不是已经注释或重复的旧片段）：

| 位置                                                                   | 代码片段                              | 问题 |
| -------------------------------------------------------------------- | --------------------------------- | -- |
| IPM-Instrument 计算                                                    | \`\`\`python                      |    |
| mean\_1\_1 = tf.reduce\_mean(w\_1\_1 \* I\_1\_1, axis=0)             |                                   |    |
| \`\`\`                                                               | 如果 `i_1_1` 为空，`reduce_mean`→`nan` |    |
| IPM-Confounder 计算                                                    | \`\`\`python                      |    |
| mean\_C\_0 = tf.reduce\_mean(sample\_weight\_0 \* rep\_C\_0, axis=0) |                                   |    |
| \`\`\`                                                               | `i0` 为空时同理                        |    |
| IPM-Adjustment 计算                                                    | \`\`\`python                      |    |
| mean\_A\_1 = tf.reduce\_mean(rep\_A\_1, axis=0)                      |                                   |    |
| \`\`\`                                                               | `i1` 为空时同理                        |    |

> 你的前一次修改已经把一部分 `reduce_mean` 换成了 `utils.safe_mean`，但文件里还有 **第二份类定义**（或未删干净的旧代码块），编译时 *最后出现的那份* 仍然调用 `tf.reduce_mean`，于是 NaN 依旧。

---

### 解决办法

1. **只保留一份网络类定义**
   *Module.py* 现在同时存在两套几乎相同的实现（搜索 `class Net(` 可以看到）。Python 会按文件顺序执行，后出现的覆盖前面的。把旧的、已注释的或调试残留的代码统统删除，保证 **唯一且干净** 的实现。

2. **把所有可能为空的均值改成安全版本**

   ```python
   def safe_mean(x, axis=0):
       # x 可能是空张量；先算 n，再用 divide_no_nan 避免 0/0
       n   = tf.cast(tf.shape(x)[0], tf.float32)
       sum = tf.reduce_sum(x, axis=axis)
       return tf.math.divide_no_nan(sum, n)
   ```

   然后在 IPM 的 6 处均值里全部使用 `utils.safe_mean`（不仅是 Instrument，还包括 Confounder/Adjustment）——示例：

   ```python
   mean_C_0 = utils.safe_mean(self.sample_weight_0 * self.rep_C_0, axis=0)
   ```

3. **阈值过严也会造成空 bucket**
   如果 `t_threshold`、`y_threshold` 设得太极端，也会让某些组永远为空，训练初期就会 NaN。调低阈值或在构造 `i_*_*` 后加断言/日志，确认每个 batch 至少有 1 条样本落入每个 bucket。

4. **其它小修**（不会直接引起 NaN，但建议一起改）

   * 在 `build_graph` 里有一处仍然写成

     ```python
     input=tf.concat(self.rep_I, axis=1)
     ```

     应改为 `input=self.rep_I`，否则维度不匹配。
   * 你的 `utils.py` 里目前 **存在两个 `safe_mean` 版本**；留下安全版即可，避免歧义。

---

#### 快速验证

清理完文件后：

```python
# 训练前插入：
with tf.control_dependencies([tf.debugging.check_numerics(loss, 'loss_nan_guard')]):
    train_op = optimizer.minimize(loss)
```

如果再出现 NaN，TensorFlow 会抛出 `InvalidArgumentError` 并给出具体张量名字，便于继续排查。

只要上述 3 处全部换成 `safe_mean`，并保证 bucket 不为空，`loss` 将会恢复为有穷值并随训练而下降。
