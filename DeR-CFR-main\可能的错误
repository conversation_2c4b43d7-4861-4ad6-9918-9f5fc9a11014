`loss = NaN` 依然出现的原因并不是单一的，而是 **代码里同时存在 4 处仍可产生 NaN 的“隐形炸弹”**。
下面按优先级列出，每一条都给出定位行、成因分析与修复方案。

| #     | 触发点                                      | 为什么会产生 NaN                                                                                                                                                                                                                | 关键行 |
| ----- | ---------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| **1** | **仍对空张量直接 `reduce_mean`**                | 在 IPM 部分你只把 **Instrument** 分支换成了 `safe_mean`，但 **Confounder** 分支保留了原句<br>`tf.reduce_mean(self.sample_weight_0 * self.rep_C_0, axis=0)` 等。当某一 mini-batch 中恰好没有 `t=0` 或 `t=1` 的样本时，`reduce_mean([])=NaN`，总 `loss` 也随之变 NaN。 |     |
| **2** | **错误调用 `tf.concat(self.rep_A, axis=1)`** | 这里把单个 `Tensor` 当成列表传给 `tf.concat`（同样的错误在旧版 `rep_I` 上也出现过），理论上会直接抛 `TypeError`，但如果被某些 IDE 静默包装，返回的是一个空 Tensor，再次导致下游均值和方差为 NaN。                                                                                            |     |
| **3** | **`safe_mean` 本身仍不“安全”**                 | 当输入为空时分支<br>`tf.zeros_like(x[0])` 仍会索引第 0 行，运行期依旧越界。结果在图构建阶段不报错，但一旦分支被执行，就触发 NaN。                                                                                                                                         |     |
| **4** | **`sample_weight` 无约束地被 Adam 更新**        | `train_balance` 每步都更新整张 `sample_weight` 矩阵，但 `loss_w=‖Σw/Σt−1‖²` 中既无梯度裁剪也无正则，几步之后权重即可飙到 1e20 量级，平方后溢出为 Inf→NaN。                                                                                                           |     |

---

## 一次性修复清单

```python
### utils.py ###########################################################
def safe_mean(x, axis=0):
    n = tf.shape(x)[0]                       # n==0 ➜ 返回全 0 向量
    num = tf.cast(n, x.dtype)
    sum_x = tf.reduce_sum(x, axis=axis)
    return tf.math.divide_no_nan(sum_x, num)   # 使用 divide_no_nan 更保险
```

```python
### Module.py ##########################################################
# ① Confounder / Adjustment 的 mean 也换成 safe_mean
mean_C_0 = utils.safe_mean(self.sample_weight_0 * self.rep_C_0, axis=0)
mean_C_1 = utils.safe_mean(self.sample_weight_1 * self.rep_C_1, axis=0)
mean_A_0 = utils.safe_mean(self.rep_A_0, axis=0)
mean_A_1 = utils.safe_mean(self.rep_A_1, axis=0)

# ② concat 单 Tensor 时直接传 Tensor
self.mu_Y_A, _, _, _, _ = self.output(
        input=self.rep_A,          # ← 改这里
        dim_in=self.FLAGS.rep_dim,
        ...
)

# 同理，treatment 分支也应是 input=self.rep_I
```

```python
### train_balance 的学习率 / 频率 ######################################
# 把 sample_weight 的优化器学习率降两到三个数量级，或等主网先收敛后再启用
self.train_balance = tf.compat.v1.train.AdamOptimizer(
        self.lr * 1e-3, 0.5).minimize(self.loss_balance, var_list=W_vars)
```

> **调试技巧**：在 `setup_train_ops` 后加
> `tf.debugging.check_numerics(self.loss, 'loss_nan_guard')`，第一时间能定位哪一项先变 NaN。

---

### 为什么 `y_acc` 一直 ≈ 0.858？

网络一旦在 **第 0 步** 把任一梯度炸成 NaN，后续权重更新全是 NaN；而 `sigmoid_cross_entropy` 在 NaN logits 时输出 0.693⋯，梯度又是 0，导致模型再也学不动。此时预测恒为 0/1 的基线概率，所以 `y_acc` 固定在数据集中多数类的占比（≈ 85.8%）。

按上面四条全部修复后，再跑一次 `train.py`，`loss` 应该能正常下降，`y_acc` 也会随着 epoch 继续提升。
