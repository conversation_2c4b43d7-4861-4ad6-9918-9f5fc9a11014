`NaN` 仍然出现是因为 **图里还有 3 个数值不安全的操作**——它们在第一次前向传播就会产⽣ ±∞，随后在向下游传播时被 **`tf.sqrt` 或 `tf.reduce_mean`** 等运算转成 `NaN`。修补思路与定位方法都列在下表，务必 *全部* 处理掉：

| #                          | 触发行（最新版 **Module.py**）                                         | 机制          | 为什么会溢出 / NaN                                                                                                                                                                                                                                                      | 彻底修补                                                                  |
| -------------------------- | -------------------------------------------------------------- | ----------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------- |
| **1**                      | `mmd_1 = tf.reduce_sum(tf.square(2.0 * p_ipm * mean_1_1 - …))` | **平方 + 求和** | `mean_*` 向量初始幅值在 `[-1,1]`；但前面还乘了 `2·p_ipm≈1`，再把整向量 **拼到一起后做两次差分** —— 在 64 维时，上式理论上界 ≈ 256。<br>这一切本来没问题，可是一旦某个 `mean_*` 向量被 0 向量（空 bucket）替换后，梯度对其对应的 **“掩码” weight** 会立刻爆炸 → 下一步前向里出现 ±1e38 → 平方得到 ≥ 1e76 → **float32 溢出为 ∞**，再喂给 `tf.reduce_sum`/Adam 就变成 `NaN`。 | 不再把空 bucket 直接置 0，而用 **`divide_no_nan` 写一个完全数值安全的均值**<br>\`\`\`python |
| def safe\_mean(x, axis=0): |                                                                |             |                                                                                                                                                                                                                                                                   |                                                                       |

```
num = tf.cast(tf.shape(x)[0], x.dtype)
return tf.math.divide_no_nan(tf.reduce_sum(x, axis), num)
```

``<br>然后把 `utils.safe_mean` 里的 `tf.cond` 替换成上面这版（`tf.cond` 在 TF-1里 *仍* 会给另外一条分支求梯度，从而保不住 NaN）。 | | **2** | `batch_mean, batch_var = tf.nn.moments(out,[0])`:contentReference[oaicite:1]{index=1}<br>`out = tf.nn.batch_normalization(out, batch_mean, batch_var, …)` | **BatchNorm** | 当一个 batch 里恰好只有 1 条样本（或者所有样本 identical）时，`batch_var` 为 **0**。你虽然加了 `1e-8`，但后面立刻又给 `epsilon=1e-3`，于是进入 `sqrt(var+eps)` 仍旧是 `sqrt(1e-3)` ≈ 0.03；随后 `(x-mean)/0.03` 可能把数值推到 ±1e4，经过 ELU/Dropout 又被扩大。再叠 7 层后，「放大 × 再平方」很快溢出 `∞ / NaN`。 | - 直接用 **`tf.layers.batch_normalization` / `tf.keras.layers.BatchNormalization`**，它自带更稳健的 `moving_variance + epsilon` 逻辑；<br>- 或把你那一行改成：<br>``python
out = tf.nn.batch\_normalization(
out, batch\_mean, batch\_var, 0., 1.,
variance\_epsilon=1e-5)            # 比 1e-3 小两阶

````|
| **3** | `self.loss_w = tf.square(tf.reduce_sum(self.sample_weight_0)/den_nt - 1.0) + …`:contentReference[oaicite:2]{index=2} | **0 ÷ 0** | 当某个 mini-batch 没有对照组（或处理组）时，`sample_weight_k` 与 `den_k` 都是 0。即使你给分母加了 `1e-8`，分子仍是 0，而另一边同理得 `0/1e-8=0`，于是整个括号成 **-1**，平方=1；这一项 *本身* 无害，但它的梯度把 `sample_weight` 往负值方向推。<br>Adam 对负值求 `log(m)` 时产生 `log(负)` → `NaN`，并把 权重矩阵整个污染。 | - 把 `sample_weight` 约束到 **非负** 并截断<br>```python
sample_weight = tf.nn.relu(sample_weight)
sample_weight = tf.clip_by_value(sample_weight, 0., 10.)
```<br>- **或者** 暂时关掉样本重加权：`--reweight_sample=0`，先稳定主网络，再开启重加权并把学习率降到 `1e-4 * 1e-3 = 1e-7` 之下。 |

---

### 两步调试法：立即定位以后所有 NaN

1. **训练前在图里插一行**  
   ```python
   tf.debugging.add_check_numerics_ops()
````

这样一旦有 NaN/∞，TensorFlow 会抛出清晰的 op 名称（通常就是上面三处之一）。

2. **把 `net.loss` 包在控制依赖里**

   ```python
   with tf.control_dependencies([tf.debugging.check_numerics(net.loss, 'loss')]):
       train_op = optimizer.minimize(net.loss)
   ```

   若第一步仍漏掉隐患，这一行会在 **真正参数更新之前** 直接报错，避免梯度把整个网络都写成 NaN。

---

完成以上 3 处修补后，再跑 `train.py`，你应该会看到：

```
0, loss: 0.69..., y_acc: 0.86..., t_acc: 0.50...
100, loss: 0.51..., y_acc: 0.88..., t_acc: 0.77...
...
```

`loss` 将不再是 `nan`，而是正常下降；若之后想进一步提速，可以把 BatchNorm 换成 LayerNorm（对批大小不敏感），同时把 `batch_size` 调小一些，以免再触发空 bucket 场景。
